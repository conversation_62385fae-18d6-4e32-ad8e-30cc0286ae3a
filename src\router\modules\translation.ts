const Layout = () => import("@/layout/index.vue");

export default {
  path: "/systems/translations/management",
  name: "Translation",
  component: Layout,
  redirect: "/systems/translations",
  meta: {
    icon: "ri:translate-2",
    title: "Translation Management",
    rank: 5
  },
  children: [
    {
      path: "/systems/translations",
      name: "TranslationIndex",
      component: () => import("@/views/system/translation/index.vue"),
      meta: {
        title: "Translation Management",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
