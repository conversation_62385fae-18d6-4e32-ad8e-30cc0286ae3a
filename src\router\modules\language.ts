const Layout = () => import("@/layout/index.vue");

export default {
  path: "/systems/languages/management",
  name: "Language",
  component: Layout,
  redirect: "/systems/languages",
  meta: {
    icon: "ri:global-line",
    title: "Language Management",
    rank: 4
  },
  children: [
    {
      path: "/systems/languages",
      name: "LanguageIndex",
      component: () => import("@/views/system/language/index.vue"),
      meta: {
        title: "Language Management",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
