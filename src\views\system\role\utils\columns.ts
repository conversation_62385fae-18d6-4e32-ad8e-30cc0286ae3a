import dayjs from "dayjs";
import { h } from "vue";
import { ElTag } from "element-plus";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "name",
    align: "left",
    sortable: "custom",
    width: 210,
    headerRenderer: () => $t("Name")
  },
  {
    prop: "displayName",
    sortable: false,
    align: "left",
    width: 200,
    headerRenderer: () => $t("Display name")
  },
  {
    prop: "description",
    align: "left",
    minWidth: 150,
    headerRenderer: () => $t("Description")
  },
  {
    prop: "isSystem",
    sortable: "custom",
    align: "center",
    width: 100,
    headerRenderer: () => $t("System Role"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.isSystem ? "warning" : "info",
          size: "small"
        },
        () => (row.isSystem ? $t("Yes") : $t("No"))
      );
    }
  },
  {
    prop: "status",
    sortable: "custom",
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        active: {
          type: "success",
          text: $t("Active"),
          class: "bg-green-100 text-green-800",
          icon: "ri:checkbox-circle-fill",
          iconClass: "text-green-600"
        },
        inactive: {
          type: "danger",
          text: $t("Inactive"),
          class: "bg-red-100 text-red-800",
          icon: "ri:close-circle-fill",
          iconClass: "text-red-600"
        }
      };

      const config = statusColors[row.status] || statusColors.active;

      return h(
        "span",
        {
          class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}`
        },
        [
          h(IconifyIconOnline, {
            icon: config.icon,
            class: `w-3 h-3 mr-1.5 ${config.iconClass}`
          }),
          config.text
        ]
      );
    }
  },
  {
    prop: "priority",
    sortable: "custom",
    align: "center",
    width: 80,
    headerRenderer: () => $t("Priority")
  },
  {
    prop: "isSystem",
    sortable: "custom",
    align: "center",
    width: 100,
    headerRenderer: () => $t("System Role"),
    cellRenderer: ({ row }) => {
      const isSystem = row.isSystem;
      const config = {
        icon: isSystem ? "ri:shield-check-fill" : "ri:user-line",
        iconClass: isSystem ? "text-orange-600" : "text-blue-600",
        class: isSystem ? "bg-orange-100" : "bg-blue-100"
      };

      return h(
        "span",
        {
          class: `inline-flex items-center justify-center w-8 h-8 rounded-full ${config.class}`
        },
        h(IconifyIconOnline, {
          icon: config.icon,
          class: `w-4 h-4 ${config.iconClass}`
        })
      );
    }
  },
  {
    prop: "description",
    align: "left",
    minWidth: 150,
    headerRenderer: () => $t("Description")
  },
  {
    prop: "createdAt",
    sortable: true,
    align: "left",
    width: 130,
    formatter: ({ createdAt }) =>
      dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss"),
    headerRenderer: () => $t("Created at")
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
