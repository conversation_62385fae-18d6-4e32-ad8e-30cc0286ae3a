import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/organization/utils/type";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getOrganizations = (params?: object) => {
  return http.request<Result>("get", "/api/auth/organizations", {
    params
  });
};

export const getOrganizationById = (uuid: string) => {
  return http.request<Result>("get", `/api/auth/organizations/${uuid}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createOrganization = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/organizations", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateOrganizationById = (uuid: string, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/organizations/${uuid}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteOrganizationById = (uuid: string) => {
  return http.request<Result>("delete", `/api/auth/organizations/${uuid}`);
};

export const bulkDeleteOrganizations = (data: { ids: string[] }) => {
  return http.request<Result>("delete", "/api/auth/organizations/bulk-delete", {
    data
  });
};

/*
 ***************************
 *   Force Delete Operations
 ***************************
 */
export const destroyOrganizationById = (uuid: string) => {
  return http.request<Result>(
    "delete",
    `/api/auth/organizations/${uuid}/force`
  );
};

export const bulkDestroyOrganizations = (data: { ids: string[] }) => {
  return http.request<Result>("delete", "/api/auth/organizations/bulk/force", {
    data
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreOrganizationById = (uuid: string) => {
  return http.request<Result>("put", `/api/auth/organizations/${uuid}/restore`);
};

export const bulkRestoreOrganizations = (data: { ids: string[] }) => {
  return http.request<Result>("put", "/api/auth/organizations/bulk-restore", {
    data
  });
};

/*
 ***************************
 *   Dropdown & Utility Operations
 ***************************
 */
export const dropdownOrganizations = () => {
  return http.request<Result>("get", "/api/auth/organizations/dropdown");
};

/*
 ***************************
 *   File Upload Operations
 ***************************
 */
export const uploadOrganizationLogo = (file: File) => {
  const formData = new FormData();
  formData.append("logo", file);

  return http.request<Result>("post", "/api/auth/organizations/upload-logo", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
