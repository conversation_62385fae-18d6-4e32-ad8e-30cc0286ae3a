import { h } from "vue";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";
import dayjs from "dayjs";
import { ElAvatar } from "element-plus";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    label: "",
    prop: "avatar",
    width: 100,
    cellRenderer: ({ row }) =>
      h(
        "div",
        {
          class: "flex items-center justify-center"
        },
        [
          h(ElAvatar, {
            size: 60,
            src: row.avatar,
            icon: "User",
            class: "border border-gray-200"
          })
        ]
      )
  },
  {
    prop: "username",
    align: "left",
    sortable: true,
    minWidth: 280,
    headerRenderer: () => $t("User"),
    cellRenderer: ({ row }) => {
      const genderConfig = {
        male: { icon: "mdi:gender-male", class: "text-blue-500" },
        female: { icon: "mdi:gender-female", class: "text-pink-500" },
        other: { icon: "mdi:gender-transgender", class: "text-purple-500" }
      };
      const currentGender = genderConfig[row.gender] || genderConfig.other;

      return h("div", { class: "flex flex-col justify-between py-2" }, [
        h("div", { class: "ml-4" }, [
          row.email &&
            h("div", { class: "flex text-xs font-medium text-gray-900" }, [
              row.fullName || $t("Unnamed"),
              row.gender &&
                h(IconifyIconOnline, {
                  icon: currentGender.icon,
                  class: `ml-1.5 w-3 h-3 ${currentGender.class}`
                })
            ]),
          h("div", { class: "flex items-center" }, [
            h(
              "span",
              { class: "text-sm text-gray-500 mr-2" },
              `@${row.username}`
            )
          ]),
          row.email &&
            h(
              "div",
              { class: "text-xs text-gray-500 mt-1 flex items-center" },
              [
                h(IconifyIconOnline, {
                  icon: "mdi:email-outline",
                  class: "w-4 h-4 mr-1.5"
                }),
                h("span", row.email)
              ]
            ),
          row.phone &&
            h(
              "div",
              { class: "text-xs text-gray-500 mt-1 flex items-center" },
              [
                h(IconifyIconOnline, {
                  icon: "mdi:phone-outline",
                  class: "w-4 h-4 mr-1.5"
                }),
                h("span", row.phone)
              ]
            )
        ])
      ]);
    }
  },
  {
    prop: "lastLoginAt",
    align: "center",
    width: 180,
    headerRenderer: () => $t("Last Login At"),
    formatter: ({ lastLoginAt }) => {
      if (lastLoginAt) {
        return dayjs(lastLoginAt).format("YYYY-MM-DD HH:mm");
      }
      return "—";
    }
  },
  {
    prop: "lastLoginIp",
    align: "center",
    width: 150,
    headerRenderer: () => $t("Last Login IP")
  },
  {
    prop: "status",
    sortable: false,
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        active: {
          type: "success",
          text: $t("Active"),
          class: "bg-green-100 text-green-800",
          icon: "ri:checkbox-circle-fill",
          iconClass: "text-green-600"
        },
        inactive: {
          type: "danger",
          text: $t("Inactive"),
          class: "bg-red-100 text-red-800",
          icon: "ri:close-circle-fill",
          iconClass: "text-red-600"
        }
      };

      const config = statusColors[row.status] || statusColors.active;

      return h(
        "span",
        {
          class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}`
        },
        [
          h(IconifyIconOnline, {
            icon: config.icon,
            class: `w-3 h-3 mr-1.5 ${config.iconClass}`
          }),
          config.text
        ]
      );
    }
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
