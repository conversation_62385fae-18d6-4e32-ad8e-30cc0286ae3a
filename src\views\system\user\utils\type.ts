export interface FormItemProps {
  id?: number;
  name: string;
  email: string;
  password?: string;
  password_confirmation?: string;
  avatar?: string;
  phone?: string;
  address?: string;
  role_id?: number;
  is_active?: boolean;
  email_verified_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface UserFilterProps {
  name?: string;
  email?: string;
  role_id?: number;
  is_active?: boolean;
  isTrashed?: string;
}
