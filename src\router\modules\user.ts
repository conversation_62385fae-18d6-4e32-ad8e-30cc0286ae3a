import { $t } from "@/plugins/i18n";

const Layout = () => import("@/layout/index.vue");

export default {
  path: "/systems/users/management",
  name: "User",
  component: Layout,
  redirect: "/systems/users",
  meta: {
    icon: "ep:user",
    title: $t("User Management"),
    rank: 3
  },
  children: [
    {
      path: "/systems/users",
      name: "UserManagement",
      component: () => import("@/views/system/user/index.vue"),
      meta: {
        title: $t("User Management"),
        showLink: true,
        auths: ["user.read"]
      }
    }
  ]
} satisfies RouteConfigsTable;
