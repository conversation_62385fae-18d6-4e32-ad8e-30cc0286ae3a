const Layout = () => import("@/layout/index.vue");

export default {
  path: "/systems/roles/management",
  name: "Role",
  component: Layout,
  redirect: "/systems/roles",
  meta: {
    icon: "ri:global-line",
    title: "Role Management",
    rank: 4
  },
  children: [
    {
      path: "/systems/roles",
      name: "RoleIndex",
      component: () => import("@/views/system/role/index.vue"),
      meta: {
        title: "Role Management",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
