<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent, watch } from "vue";
import { useRoleHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

// Lazy load components
const RoleDrawerForm = defineAsyncComponent(
  () => import("./components/RoleDrawerForm.vue")
);

const RoleFilterForm = defineAsyncComponent(
  () => import("./components/RoleFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetRoles,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  roleFormRef,
  handleSubmit,
  handleFilter,
  handleBulkPermanentDelete,
  handleBulkRestore,
  handlePermanentDelete,
  handleRestore
} = useRoleHook();

const handleEdit = (row: any) => {
  const permissions = row.permissions.map((permission: any) =>
    Number(permission.id)
  );
  drawerValues.value = { ...clone(row, true), permissions: permissions };
  drawerVisible.value = true;
};

onMounted(() => {
  nextTick(() => {
    fnGetRoles();
  });
});

watch(
  () => multipleSelection.value,
  value => {
    const a = multipleSelection.value.some(item => item.isSystem);
    console.log("a--->", multipleSelection.value);
  }
);
</script>

<template>
  <div class="main">
    <div
      ref="contentRef"
      :class="['flex', deviceDetection() ? 'flex-wrap' : '']"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('Role Management')"
        :columns="columns"
        @refresh="fnGetRoles"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('role.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                :icon="
                  hasAuth('role.create')
                    ? 'flat-color-icons:plus'
                    : 'icons8:plus'
                "
                width="18px"
              />
            </el-button>
          </el-tooltip>
          <template v-if="filterRef.isTrashed === 'yes'">
            <el-tooltip :content="$t('Restore')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 && !hasAuth('role.delete'))
                "
                @click="() => handleBulkRestore()"
              >
                <IconifyIconOnline
                  icon="tabler:restore"
                  width="18px"
                  :class="{
                    'text-blue-600':
                      multipleSelection.length > 0 && !hasAuth('role.delete')
                  }"
                />
              </el-button>
            </el-tooltip>
            <el-tooltip :content="$t('Bulk Destroy')" placement="top">
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length === 0 ||
                  (multipleSelection.length > 0 && !hasAuth('role.destroy'))
                "
                @click="() => handleBulkPermanentDelete()"
              >
                <IconifyIconOnline
                  icon="tabler:trash-x-filled"
                  width="18px"
                  :class="{
                    'text-red-700':
                      multipleSelection.length > 0 && !hasAuth('role.destroy')
                  }"
                />
              </el-button>
            </el-tooltip>
          </template>
          <template v-else>
            <el-tooltip
              v-if="filterRef.isTrashed === 'no'"
              :content="$t('Bulk Destroy')"
              placement="top"
            >
              <el-button
                type="text"
                class="font-bold text-[16px]"
                :disabled="
                  multipleSelection.length == 0 ||
                  (multipleSelection.length > 0 && !hasAuth('role.delete')) ||
                  multipleSelection.some(item => item.isSystem)
                "
                @click="() => handleBulkDelete()"
              >
                <IconifyIconOnline
                  icon="tabler:trash"
                  width="18px"
                  :class="{
                    'text-red-700':
                      (multipleSelection.length > 0 &&
                        !hasAuth('role.delete')) ||
                      !multipleSelection.some(item => item.isSystem)
                  }"
                />
              </el-button>
            </el-tooltip>
          </template>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            adaptive
            border
            :adaptiveConfig="{ offsetBottom: 108 }"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @sort-change="fnHandleSortChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @selection-change="fnHandleSelectionChange"
          >
            <template #operation="{ row }">
              <el-dropdown split-button trigger="click" size="small">
                {{ $t("Action") }}
                <template #dropdown>
                  <el-dropdown-menu class="min-w-[130px]">
                    <el-dropdown-item disabled>
                      {{ $t("Action") }}
                    </el-dropdown-item>
                    <template v-if="filterRef.isTrashed == 'no'">
                      <el-dropdown-item
                        :disabled="!hasAuth('role.edit')"
                        @click="handleEdit(row)"
                      >
                        <IconifyIconOnline
                          icon="material-symbols:edit"
                          class="text-blue-600"
                        />
                        <span class="ml-2">
                          {{ $t("Edit") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('role.delete') || row.isSystem"
                        @click="handleDelete(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item
                        :disabled="!hasAuth('role.delete')"
                        @click="handleRestore(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:restore"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Restore") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('role.destroy')"
                        @click="handlePermanentDelete(row)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash-x"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>
    <RoleDrawerForm
      ref="roleFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          roleFormRef?.resetForm();
          drawerValues = {
            status: 'active'
          };
        }
      "
    />

    <RoleFilterForm
      ref="filterFormRef"
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="
        () => {
          filterRef = { isTrashed: 'no' };
          fnGetRoles();
        }
      "
    />
  </div>
</template>

<style lang="scss" scoped>
.main {
  @apply p-4;
}
</style>
