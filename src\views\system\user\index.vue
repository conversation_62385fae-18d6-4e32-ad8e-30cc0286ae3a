<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useUserHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

// Lazy load components
const UserDrawerForm = defineAsyncComponent(
  () => import("./components/UserDrawerForm.vue")
);

const UserFilterForm = defineAsyncComponent(
  () => import("./components/UserFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetUsers,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  userFormRef,
  handleSubmit,
  handleFilter,
  handleBulkDestroy,
  handleBulkRestore,
  handleDestroy,
  handleRestore
} = useUserHook();

const handleEdit = (row: any) => {
  drawerValues.value = clone(row, true);
  drawerVisible.value = true;
};

onMounted(() => {
  nextTick(() => {
    fnGetUsers();
  });
});
</script>

<template>
  <div class="main">
    <div
      v-if="!deviceDetection()"
      class="w-full h-full flex flex-col"
    >
      <PureTableBar
        class="!w-full"
        style="transition: width 220ms cubic-bezier(0.4, 0, 0.2, 1)"
        :title="$t('User Management')"
        :columns="columns"
        @refresh="fnGetUsers"
        @filter="filterVisible = true"
      >
        <template #buttons>
          <el-tooltip :content="$t('Create new')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('user.create')"
              @click="
                () => {
                  drawerVisible = true;
                }
              "
            >
              <IconifyIconOnline
                icon="ep:plus"
                class="text-[18px]"
              />
            </el-button>
          </el-tooltip>
          <el-tooltip
            v-if="multipleSelection.length > 0"
            :content="$t('Bulk Delete')"
            placement="top"
          >
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('user.delete')"
              @click="handleBulkDelete"
            >
              <IconifyIconOnline
                icon="ep:delete"
                class="text-red-500 text-[18px]"
              />
            </el-button>
          </el-tooltip>
          <el-tooltip
            v-if="multipleSelection.length > 0 && filterRef.isTrashed === 'yes'"
            :content="$t('Bulk Restore')"
            placement="top"
          >
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('user.restore')"
              @click="handleBulkRestore"
            >
              <IconifyIconOnline
                icon="ep:refresh-right"
                class="text-green-500 text-[18px]"
              />
            </el-button>
          </el-tooltip>
          <el-tooltip
            v-if="multipleSelection.length > 0 && filterRef.isTrashed === 'yes'"
            :content="$t('Bulk Destroy')"
            placement="top"
          >
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('user.force-delete')"
              @click="handleBulkDestroy"
            >
              <IconifyIconOnline
                icon="tabler:trash-x"
                class="text-red-800 text-[18px]"
              />
            </el-button>
          </el-tooltip>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <PureTable
            ref="tableRef"
            border
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            row-key="id"
            alignWhole="center"
            showOverflowTooltip
            table-layout="auto"
            :loading="loading"
            :size="size"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="fnHandleSelectionChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @sort-change="fnHandleSortChange"
          >
            <template #operation="{ row }">
              <el-button
                v-if="hasAuth('user.edit')"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                <IconifyIconOnline
                  icon="ep:edit"
                  class="mr-1"
                />
                {{ $t("Edit") }}
              </el-button>
              <el-dropdown>
                <el-button
                  type="primary"
                  size="small"
                  class="ml-2"
                >
                  {{ $t("Edit") }}
                </el-button>
                
                <el-dropdown
                  v-if="hasAuth('user:delete') || hasAuth('user:restore')"
                  @command="(command: string) => handleMenuClick(command, row)"
                >
                  <el-button
                    type="danger"
                    size="small"
                    :icon="useRenderIcon('ep:arrow-down')"
                  >
                    {{ $t("More") }}
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        :disabled="!hasAuth('user.delete')"
                        @click="handleDelete(row.id)"
                      >
                        <IconifyIconOnline
                          icon="ep:delete"
                          class="text-red-500"
                        />
                        <span class="ml-2">
                          {{ $t("Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                    <template v-else>
                      <el-dropdown-item
                        :disabled="!hasAuth('user.restore')"
                        @click="handleRestore(row.id)"
                      >
                        <IconifyIconOnline
                          icon="ep:refresh-right"
                          class="text-green-500"
                        />
                        <span class="ml-2">
                          {{ $t("Restore") }}
                        </span>
                      </el-dropdown-item>
                      <el-dropdown-item
                        :disabled="!hasAuth('user.force-delete')"
                        @click="handleDestroy(row.id)"
                      >
                        <IconifyIconOnline
                          icon="tabler:trash-x"
                          class="text-red-800"
                        />
                        <span class="ml-2">
                          {{ $t("Delete") }}
                        </span>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <UserDrawerForm
      ref="userFormRef"
      v-model:visible="drawerVisible"
      v-model:values="drawerValues"
      @submit="handleSubmit"
      @close="
        () => {
          userFormRef.value?.resetForm();
          drawerValues = { status: 'active', isVerified: false };
        }
      "
    />

    <UserFilterForm
      v-model:visible="filterVisible"
      v-model:values="filterRef"
      @submit="handleFilter"
      @reset="() => { filterRef = { isTrashed: false }; fnGetUsers(); }"
    />
  </div>
</template>
