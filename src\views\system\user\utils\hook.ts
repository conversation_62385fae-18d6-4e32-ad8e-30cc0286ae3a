import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getUsers,
  deleteUserById,
  bulkDeleteUsers,
  destroyUserById,
  bulkDestroyUsers,
  restoreUserById,
  bulkRestoreUsers,
  createUser,
  updateUserById
} from "./auth-api";
import type { UserFilterProps } from "./type";

export function useUserHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const filterRef = ref<UserFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "active",
    isVerified: false
  });
  const userFormRef = ref();

  /*
   ***************************
   *   API Data Fetching
   ***************************
   */
  const fnGetUsers = async () => {
    loading.value = true;
    try {
      const response = await getUsers(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;

      console.log("Re vale***************>", records.value);
    } catch (e) {
      console.error("Get User error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Create & Update Operations
   ***************************
   */
  const fnHandleCreateUser = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createUser(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), { type: "success" });
        await fnGetUsers();
        drawerVisible.value = false;
        return true;
      }
      message(response.message || $t("Create failed"), { type: "error" });
      return false;
    } catch (e) {
      console.error("Create User error:", e);
      message(e.response?.data?.message || e?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleUpdateUser = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateUserById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetUsers();
        drawerVisible.value = false;
        return true;
      }
      message(response.message || $t("Update failed"), { type: "error" });
      return false;
    } catch (e) {
      console.error("Update User error:", e);
      message(e.response?.data?.message || e?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Soft Delete Operations
   ***************************
   */
  const fnHandleDelete = async (id: number) => {
    try {
      loading.value = true;
      const response = await deleteUserById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), { type: "success" });
        await fnGetUsers();
        return true;
      }
      message(response.message || $t("Delete failed"), { type: "error" });
      return false;
    } catch (e) {
      console.error("Delete User error:", e);
      message(e.response?.data?.message || e?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      
      loading.value = true;
      const response = await destroyUserById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetUsers();
        return true;
      }
      message(response.message || $t("Delete failed"), { type: "error" });
      return false;
    } catch (e) {
      console.error("Destroy User error:", e);
      message(e.response?.data?.message || e?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleBulkDestroy = async (ids: number[]) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      
      loading.value = true;
      const response = await bulkDestroyUsers({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetUsers();
        return true;
      }
      message(response.message || $t("Delete failed"), { type: "error" });
      return false;
    } catch (e) {
      console.error("Bulk Destroy Users error:", e);
      message(e.response?.data?.message || e?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Restore Operations
   ***************************
   */
  const fnHandleRestore = async (id: number) => {
    try {
      loading.value = true;
      const response = await restoreUserById(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), { type: "success" });
        await fnGetUsers();
        return true;
      }
      message(response.message || $t("Restore failed"), { type: "error" });
      return false;
    } catch (e) {
      console.error("Restore User error:", e);
      message(e.response?.data?.message || e?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const fnHandleBulkRestore = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkRestoreUsers({ ids });
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetUsers();
        return true;
      }
      message(response.message || $t("Restore failed"), { type: "error" });
      return false;
    } catch (e) {
      console.error("Bulk Restore Users error:", e);
      message(e.response?.data?.message || e?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Table Event Handlers
   ***************************
   */
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = ({ prop, order }: any) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetUsers();
  };

  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetUsers();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetUsers();
  };

  /*
   ***************************
   *   UI Action Handlers
   ***************************
   */
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(id);
    } catch (error) {
      if (error !== "cancel") {
        console.error("Delete confirmation error:", error);
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await fnHandleBulkDelete(ids);
    } catch (error) {
      if (error !== "cancel") {
        console.error("Bulk delete confirmation error:", error);
      }
    }
  };

  const handleDestroy = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDestroy(id);
    } catch (error) {
      if (error !== "cancel") {
        console.error("Destroy confirmation error:", error);
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDeleteUsers({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), { type: "success" });
        await fnGetUsers();
      } else {
        message(response.message || $t("Delete failed"), { type: "error" });
      }
    } catch (error) {
      if (error !== "cancel") {
        console.error("Bulk restore confirmation error:", error);
      }
    }
  };

  /*
   ***************************
   *   Form Handlers
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    if (values.id != null) {
      await fnHandleUpdateUser(Number(values.id), values);
      return;
    }
    const success = await fnHandleCreateUser(values);
    if (success) {
      drawerValues.value = {
        status: "active",
        isVerified: false
      };
      userFormRef.value?.resetForm();
    }
  };

  const handleFilter = async (values: UserFilterProps) => {
    filterRef.value = values;
    pagination.currentPage = 1;
    await fnGetUsers();
  };

  /*
   ***************************
   *   Return Hook Interface
   ***************************
   */
  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    userFormRef,

    // API Handlers
    fnGetUsers,
    fnHandleCreateUser,
    fnHandleUpdateUser,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
